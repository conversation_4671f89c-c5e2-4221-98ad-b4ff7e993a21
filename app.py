import streamlit as st
import os
import utils
from PyPDF2 import PdfReader
import json
from datetime import datetime
from dotenv import load_dotenv, find_dotenv
from typing import Generator
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import prompts
from pathlib import Path
from prompts import get_prompt_template
import pandas as pd
from docx import Document
from io import BytesIO
import markdown
import base64
from chat_history_sqlite import SQLiteChatHistory
from embedding_utils import NomicEmbeddingFunction, EmbeddingManager

# Import LLM providers
from llm_providers import (
    OllamaProvider,
    OpenAIProvider,
    GeminiProvider,
    AnthropicProvider,
    OpenRouterProvider
)

# Load environment variables
load_dotenv(find_dotenv())

# dotenv_path = find_dotenv()
# if dotenv_path:
#     load_dotenv(dotenv_path)
#     print(f"Loaded .env from: {dotenv_path}")
# else:
#     print(".env file not found.")

# Provider mapping
PROVIDER_CLASSES = {
    "Ollama": <PERSON>llamaProvider,
    "OpenAI": OpenAIProvider,
    "Gemini": <PERSON><PERSON>rovider,
    "Anthropic": AnthropicProvider,
    "OpenRouter": OpenRouterProvider
}

# App configuration
st.set_page_config(
    page_title="مساعد الذكاء الاصطناعي للبحث العلمي",
    layout="wide",
    menu_items={
        'About': "مساعد ذكي مدعوم بالذكاء الاصطناعي للكتابة العلمية باستخدام عدة مزودي نماذج اللغة"
    }
)

# Add CSS for better Arabic text support
st.markdown("""
<style>
    .main .block-container {
        direction: rtl;
        text-align: right;
    }

    .stSelectbox > div > div {
        direction: rtl;
        text-align: right;
    }

    .stTextInput > div > div > input {
        direction: rtl;
        text-align: right;
    }

    .stTextArea > div > div > textarea {
        direction: rtl;
        text-align: right;
    }

    .stChatInput > div > div > input {
        direction: rtl;
        text-align: right;
    }

    /* Keep code blocks and technical content LTR */
    .stCode, code, pre {
        direction: ltr !important;
        text-align: left !important;
    }

    /* Keep metrics and numbers LTR */
    .metric-container {
        direction: ltr;
        text-align: left;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
# def initialize_session_state():
#     if "messages" not in st.session_state:
#         st.session_state.messages = []
#     if "uploaded_files" not in st.session_state:
#         st.session_state.uploaded_files = []
#     if "citations" not in st.session_state:
#         st.session_state.citations = []
#     if "selected_template" not in st.session_state:
#         st.session_state.selected_template = None
#     if "model_initialized" not in st.session_state:
#         st.session_state.model_initialized = False
#     if "llm_provider" not in st.session_state:
#         st.session_state.llm_provider = None

# initialize_session_state()
# Initialize session state with chat history
def initialize_session_state():
    if "embedding_manager" not in st.session_state:
        try:
            st.session_state.embedding_manager = EmbeddingManager()
        except Exception as e:
            st.error(f"Failed to init embedding system: {str(e)}")
            st.session_state.embedding_manager = None
    if "messages" not in st.session_state:
        st.session_state.messages = [
            {"role": "assistant", "content": "كيف يمكنني مساعدتك في الكتابة العلمية اليوم؟"}
        ]
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []
    if "chat_db" not in st.session_state:
        st.session_state.chat_db = SQLiteChatHistory()
    if "current_session" not in st.session_state:
        st.session_state.current_session = st.session_state.chat_db.create_session(
            metadata={"app": "مساعد الكتابة العلمية"}
        )
    if "uploaded_files" not in st.session_state:
        st.session_state.uploaded_files = []
    if "citations" not in st.session_state:
        st.session_state.citations = []
    if "selected_template" not in st.session_state:
        st.session_state.selected_template = None
    if "model_initialized" not in st.session_state:
        st.session_state.model_initialized = False
    if "llm_provider" not in st.session_state:
        st.session_state.llm_provider = None
    if "session_to_delete" not in st.session_state:
        st.session_state.session_to_delete = None
    if "show_delete_success" not in st.session_state:
        st.session_state.show_delete_success = None

initialize_session_state()

# Chat history functions
def save_chat_history():
    """Save chat history to a JSON file"""
    history = {
        "messages": st.session_state.messages,
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "provider": st.session_state.llm_provider.get_provider_name() if st.session_state.model_initialized else None,
            "task": task
        }
    }
    st.session_state.chat_history.append(history)

    # Save to file
    os.makedirs("chat_history", exist_ok=True)
    filename = f"chat_history/chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(history, f, ensure_ascii=False, indent=2)

def export_chat(format: str = "json"):
    """Export chat history in different formats"""
    if not st.session_state.messages:
        return None

    if format == "json":
        return json.dumps(st.session_state.messages, indent=2)
    elif format == "txt":
        return "\n".join([f"{msg['role']}: {msg['content']}" for msg in st.session_state.messages])
    elif format == "csv":
        df = pd.DataFrame(st.session_state.messages)
        return df.to_csv(index=False)
    elif format == "md":
        return "\n".join([f"**{msg['role'].capitalize()}**: {msg['content']}" for msg in st.session_state.messages])
    elif format == "docx":
        doc = Document()
        doc.add_heading('Chat History', level=1)
        for msg in st.session_state.messages:
            doc.add_paragraph(f"{msg['role'].capitalize()}:", style='Heading2')
            doc.add_paragraph(msg['content'])
        buffer = BytesIO()
        doc.save(buffer)
        return buffer.getvalue()
    return None

# Initialize sidebar
with st.sidebar:
    # Logo section with upload option
    uploaded_logo = st.file_uploader("تغيير الشعار", type=["png", "jpg", "jpeg"], key="logo_uploader")
    logo_path = "logo.png"  # Default logo
    
    if uploaded_logo:
        # Save the uploaded logo
        with open("uploaded_logo.png", "wb") as f:
            f.write(uploaded_logo.getbuffer())
        logo_path = "uploaded_logo.png"
    
    st.logo(logo_path, size="medium")
    st.title("⚙️ الاعدادات")



    # Provider selection
    provider_name = st.selectbox(
        "مزود الذكاء الاصطناعي",
        list(PROVIDER_CLASSES.keys()),
        index=0,
        help="اختر مزود نموذج اللغة المراد استخدامه"
    )

    # Get provider class
    provider_class = PROVIDER_CLASSES[provider_name]

    # Model selection based on provider
    try:
        dummy_provider = provider_class()  # Create instance to get model list
        model_list = dummy_provider.get_model_list()
        model_name = st.selectbox(
            f"نموذج {provider_class.get_provider_name()}",
            model_list,
            index=0
        )
    except Exception as e:
        st.error(f"فشل في الحصول على النماذج: {str(e)}")
        model_list = []
        model_name = ""

    # Model parameters
    col1, col2 = st.columns(2)
    with col1:
        temperature = st.slider(
            "درجة الحرارة (Temperature)",
            0.1, 1.0, 0.7,
            help="تتحكم في عشوائية النتائج (القيمة الأقل = إجابات أكثر تحديدًا)"
        )
    with col2:
        max_tokens = st.slider(
            "أقصى عدد من التوكنات (Max Tokens)",
            512, 4096, 2048,
            help="الحد الأقصى لعدد التوكنات التي سيتم توليدها"
        )

    # Initialize model
    if st.button("تهيئة النموذج"):
        try:
            st.session_state.llm_provider = provider_class(
                model=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
            st.session_state.model_initialized = True
            st.success(f"تم تهيئة {provider_class.get_provider_name()} باستخدام {model_name}!")
        except Exception as e:
            st.error(f"فشل في تهيئة النموذج: {str(e)}")

    st.divider()
    with st.sidebar:
      st.subheader("إدارة الجلسات")

    # New session button
      if st.button("✨ جلسة جديدة"):
        st.session_state.current_session = st.session_state.chat_db.create_session()
        st.session_state.messages = []
        st.rerun()

    # Session history
    st.markdown("### الجلسات السابقة")
    sessions = st.session_state.chat_db.list_sessions()

    if not sessions:
          st.info("لم يتم العثور على جلسات سابقة.")
    else:
          for session in sessions:
            cols = st.columns([3, 1, 1])
            with cols[0]:
                # Show current session indicator
                current_indicator = "🟢 " if session['session_id'] == st.session_state.current_session else ""
                st.caption(f"{current_indicator}{session['session_id']}")
                st.caption(f"{session['message_count']} رسالة")
            with cols[1]:
                if st.button("📂", key=f"load_{session['session_id']}", help="تحميل الجلسة"):
                    st.session_state.current_session = session['session_id']
                    st.session_state.messages = st.session_state.chat_db.get_messages(session['session_id'])
                    st.rerun()
            with cols[2]:
                # Don't allow deleting the current session
                if session['session_id'] != st.session_state.current_session:
                    if st.button("🗑️", key=f"delete_{session['session_id']}", help="حذف الجلسة"):
                        # Store which session to delete for confirmation
                        st.session_state.session_to_delete = session['session_id']
                        st.rerun()
                else:
                    st.caption("الحالية")

          # Show confirmation popup if a session is marked for deletion
          if hasattr(st.session_state, 'session_to_delete') and st.session_state.session_to_delete:
              @st.dialog("🗑️ حذف الجلسة")
              def confirm_delete_popup():
                  st.warning(f"⚠️ هل أنت متأكد من حذف الجلسة:")
                  st.code(st.session_state.session_to_delete)
                  st.caption("⚠️ لا يمكن التراجع عن هذا الإجراء!")

                  col1, col2 = st.columns(2)
                  with col1:
                      if st.button("✅ نعم، احذف", key="confirm_delete", type="primary", use_container_width=True):
                          session_id = st.session_state.session_to_delete
                          if st.session_state.chat_db.delete_session(session_id):
                              st.session_state.session_to_delete = None
                              st.session_state.show_delete_success = session_id
                              st.rerun()
                          else:
                              st.error(f"❌ فشل في حذف الجلسة '{session_id}'")
                              st.stop()
                  with col2:
                      if st.button("❌ إلغاء", key="cancel_delete", use_container_width=True):
                          st.session_state.session_to_delete = None
                          st.rerun()

              confirm_delete_popup()

          # Show success popup if deletion was successful
          if hasattr(st.session_state, 'show_delete_success') and st.session_state.show_delete_success:
              @st.dialog("✅ تم حذف الجلسة")
              def success_popup():
                  st.success("🎉 تم حذف الجلسة بنجاح!")
                  st.info(f"تم حذف الجلسة '{st.session_state.show_delete_success}' نهائ.")

                  if st.button("موافق", key="success_ok", type="primary", use_container_width=True):
                      st.session_state.show_delete_success = None
                      st.rerun()

              success_popup()
    with st.sidebar:
        st.divider()
        st.subheader("🔍 البحث الدلالي")

        search_query = st.text_input("البحث في جميع المستندات")
    if search_query:
        with st.spinner("البحث عن المقاطع ذات الصلة..."):
            results = st.session_state.embedding_manager.semantic_search(
                query=search_query,
                k=3  # Top 3 results
            )

        for i, result in enumerate(results):
            with st.expander(f"📄 نتيجة {i+1} (النقاط: {result['score']:.2f})"):
                st.caption(f"المصدر: {result['metadata']['filename']}")
                st.write(result['text'][:500] + "...")
                if st.button("استخدم في المحادثة", key=f"use_result_{i}"):
                    st.session_state.messages.append({
                        "role": "user",
                        "content": f"بخصوص هذا المقطع: {result['text'][:300]}..."
                    })
                    st.rerun()
    # Export options
    st.markdown("### تصدير الجلسة الحالية")
    export_format = st.selectbox(
        "الصيغة",
        ["JSON", "Text", "CSV", "HTML"],
        key="export_format"
    )

    if st.button("💾 تصدير"):
        export_data = st.session_state.chat_db.export_session(
            st.session_state.current_session,
            export_format.lower()
        )

        if export_data:
            st.download_button(
                "تحميل",
                data=export_data,
                file_name=f"chat_{st.session_state.current_session}.{export_format.lower()}",
                mime={
                    "json": "application/json",
                    "text": "text/plain",
                    "csv": "text/csv",
                    "html": "text/html"
                }[export_format.lower()]
            )
    st.subheader("سجل المحادثة | Chat History")
     # View History Button
    if st.button("عرض السجل | Show History"):
        st.write("### محادثات سابقة | Chat History")
        for i, msg in enumerate(st.session_state.messages):
            st.write(f"**{msg['role'].upper()}**: {msg['content']}")
            if i < len(st.session_state.messages)-1:
                st.divider()

     # Export options
     # Export Options
    export_format = st.selectbox(
        "صيغة التصدير | Export Format",
        ["JSON", "Word", "Text", "PDF"],
        index=0
    )
    if st.button("حفظ المحادثة | Export Chat"):
        export_data = None
        if export_format == "JSON":
            export_data = json.dumps(st.session_state.messages, indent=2, ensure_ascii=False)
        elif export_format == "Word":
            doc = Document()
            doc.add_heading('سجل المحادثة | Chat History', level=1)
            for msg in st.session_state.messages:
                doc.add_heading(msg['role'], level=2)
                doc.add_paragraph(msg['content'])
            buffer = BytesIO()
            doc.save(buffer)
            export_data = buffer.getvalue()
            file_extension = "docx"
            mime_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif export_format == "Text":
            export_data = "\n\n".join([f"{msg['role']}:\n{msg['content']}"
                                    for msg in st.session_state.messages])
            file_extension = "txt"
            mime_type = "text/plain"
        elif export_format == "PDF":
            export_data = utils.generate_pdf(st.session_state.messages)
            file_extension = "pdf"
            mime_type = "application/pdf"
        if export_data:
            st.download_button(
                f"تحميل كـ {export_format}",
                data=export_data,
                file_name=f"chat_history.{export_format.lower()}",
                mime={
                    "JSON": "application/json",
                    "Word": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "Text": "text/plain",
                    "PDF": "application/pdf"
                }[export_format]
            )

    # Clear History Button
    if st.button("مسح السجل | Clear History"):
        st.session_state.messages = [
            {"role": "assistant", "content": "تم مسح سجل المحادثة. كيف يمكنني المساعدة؟ | Chat history cleared. How can I help?"}
        ]
        st.rerun()

    st.divider()
    st.subheader("📄 معالجة ملفات PDF")
    uploaded_file = st.file_uploader(
    "رفع ورقة بحثية (PDF)",
    type="pdf",
    help="رفع ملفات PDF لاستخراج النص للمرجعية"
   )

    if uploaded_file and uploaded_file.name not in [f["name"] for f in st.session_state.uploaded_files]:
     with st.spinner(f"استخراج وفهرسة {uploaded_file.name}..."):
        try:
            # 1. Extract text from PDF
            text = utils.extract_text_from_pdf(uploaded_file)

            # 2. Store basic file info
            file_info = {
                "name": uploaded_file.name,
                "content": text,
                "upload_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            st.session_state.uploaded_files.append(file_info)

            # 3. Process in chunks (better for long papers)
            chunk_size = 1000  # characters per chunk
            chunks = [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]

            # Progress bar for chunk processing
            progress_bar = st.progress(0)
            total_chunks = len(chunks)

            for i, chunk in enumerate(chunks):
                # Update progress
                progress_bar.progress((i + 1) / total_chunks)

                # Add each chunk to vector store
                st.session_state.embedding_manager.add_to_index(
                    text=chunk,
                    metadata={
                        "filename": uploaded_file.name,
                        "chunk_num": i+1,
                        "total_chunks": total_chunks,
                        "type": "research_paper",
                        "timestamp": datetime.now().isoformat()
                    }
                )

            st.success(f"""
                ✅ تم معالجة {uploaded_file.name} بنجاح:
                - تم استخراج {len(text):,} حرف
                - تم تقسيمه إلى {total_chunks} جزء
                - تم إضافته إلى مكتبة البحث
            """)

        except Exception as e:
            st.error(f"🚨 فشلت المعالجة: {str(e)}")
            st.warning("""
                حلول شائعة:
                1. تأكد من تشغيل Ollama (`ollama serve`)
                2. تحقق من تثبيت nomic-embed-text (`ollama pull nomic-embed-text`)
                3. تأكد من أن PDF غير محمي بكلمة مرور
            """)
    # Display uploaded files
    if st.session_state.uploaded_files:
        st.markdown("**الملفات المرفوعة:**")
        for file in st.session_state.uploaded_files:
            cols = st.columns([4, 1])
            with cols[0]:
                st.caption(f"{file['name']}")
            with cols[1]:
                if st.button("🗑️", key=f"del_{file['name']}"):
                    st.session_state.uploaded_files = [
                        f for f in st.session_state.uploaded_files
                        if f["name"] != file["name"]
                    ]
                    st.rerun()

    st.divider()

    # Citation Management
    st.subheader("📚 إدارة المراجع")
    citation_tab1, citation_tab2 = st.tabs(["مزامنة Zotero", "إضافة يدوية"])

    with citation_tab1:
        if st.button("مزامنة مع Zotero"):
            try:
                zotero_cites = utils.get_zotero_citations()
                st.session_state.citations.extend(zotero_cites)
                st.success(f"تم إضافة {len(zotero_cites)} مرجع من Zotero")
            except Exception as e:
                st.error(f"فشل الاتصال بـ Zotero: {str(e)}")

    with citation_tab2:
        with st.form("add_citation_form"):
            author = st.text_input("المؤلف*", placeholder="الاسم الأخير، الأول.")
            year = st.text_input("السنة*", placeholder="2023")
            title = st.text_input("العنوان*", placeholder="عنوان الورقة")
            journal = st.text_input("المجلة", placeholder="اسم المجلة")

            if st.form_submit_button("إضافة مرجع"):
                if author and year and title:
                    st.session_state.citations.append({
                        "author": author,
                        "year": year,
                        "title": title,
                        "journal": journal if journal else "غير محدد",
                        "source": "manual"
                    })
                    st.success("تم إضافة المرجع!")
                else:
                    st.warning("يرجى ملء الحقول المطلوبة (*)")

    st.divider()

    # Template Selection
    st.subheader("📝 قوالب الأوراق العلمية")
    template_files = utils.get_template_files()
    selected_template = st.selectbox(
        "اختر قالب ورقة علمية",
        [f.replace(".json", "") for f in template_files],
        index=0
    )

    col1, col2 = st.columns(2)
    with col1:
        if st.button("تحميل القالب"):
            st.session_state.selected_template = utils.load_template(selected_template)
            st.success(f"تم تحميل قالب {selected_template}")
    with col2:
        if st.button("مسح القالب"):
            st.session_state.selected_template = None
            st.info("تم مسح القالب")

# Main app interface
st.title("🧪مساعد الذكاء الاصطناعي للبحث العلمي ")
st.caption("أداة مدعومة بالذكاء الاصطناعي للكتابة العلمية ومراجعات الأدبيات")

# Status indicators
status_cols = st.columns(3)
with status_cols[0]:
    st.metric("النموذج جاهز", "✅" if st.session_state.model_initialized else "❌")
with status_cols[1]:
    st.metric("ملفات PDF محملة", len(st.session_state.uploaded_files))
with status_cols[2]:
    st.metric("المراجع", len(st.session_state.citations))

# Task selection
task = st.radio(
    "اختر المهمة",
    ["مراجعة الأدبيات", "كتابة الورقة العلمية", "توليد الملخص", "المنهجية العلمية"],
    horizontal=True,
    index=0
)

# Template guidance
if st.session_state.selected_template:
    with st.expander("📋 إرشادات القالب", expanded=True):
        st.write(f"**{st.session_state.selected_template['name']}**")
        st.caption(st.session_state.selected_template["description"])
        if task in st.session_state.selected_template:
            st.write(st.session_state.selected_template[task])

# PDF content display
if st.session_state.uploaded_files:
    with st.expander("📄 عرض محتوى PDF", expanded=False):
        selected_pdf = st.selectbox(
            "اختر PDF للعرض",
            [f["name"] for f in st.session_state.uploaded_files],
            index=0
        )
        pdf_content = next(
            f["content"] for f in st.session_state.uploaded_files
            if f["name"] == selected_pdf
        )
        st.text_area(
            "النص المستخرج",
            pdf_content[:5000] + ("..." if len(pdf_content) > 5000 else ""),
            height=200
        )

# Citation manager
with st.expander("📚 مدير المراجع", expanded=False):
    citation_style = st.selectbox(
        "نمط الاقتباس",
        ["APA", "MLA", "Chicago", "BibTeX"],
        index=0
    )

    if st.session_state.citations:
        st.write(f"**{len(st.session_state.citations)} مرجع محفوظ**")

        # Citation table
        cols = st.columns([2, 1, 3, 2, 1])
        with cols[0]: st.write("**المؤلف**")
        with cols[1]: st.write("**السنة**")
        with cols[2]: st.write("**العنوان**")
        with cols[3]: st.write("**المجلة**")
        with cols[4]: st.write("**الإجراءات**")

        for i, citation in enumerate(st.session_state.citations):
            cols = st.columns([2, 1, 3, 2, 1])
            with cols[0]: st.write(citation.get("author", "غير معروف"))
            with cols[1]: st.write(citation.get("year", "غير معروف"))
            with cols[2]: st.write(citation.get("title", "بلا عنوان"))
            with cols[3]: st.write(citation.get("journal", "غير محدد"))
            with cols[4]:
                if st.button("🗑️", key=f"del_cite_{i}"):
                    st.session_state.citations.pop(i)
                    st.rerun()

    # Export options
export_cols = st.columns(3)
with export_cols[0]:
    st.download_button(
        "تصدير إلى Word",
        data=utils.generate_word_doc(st.session_state.citations, citation_style),
        file_name="references.docx",
        mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    )
with export_cols[1]:
    st.download_button(
        "تصدير إلى BibTeX",
        data=utils.generate_bibtex(st.session_state.citations),
        file_name="references.bib",
        mime="text/plain"
    )
with export_cols[2]:
    st.download_button(
        "تصدير إلى CSV",
        data=utils.generate_csv(st.session_state.citations),
        file_name="references.csv",
        mime="text/csv"
    )
# Main chat interface
st.divider()
# --- Enhanced Chat Interface ---
if "embedding_manager" in st.session_state:  # Only if embeddings are initialized
    # Display document context if available
    if st.session_state.messages and st.session_state.messages[-1]["role"] == "user":
        last_query = st.session_state.messages[-1]["content"]
        with st.spinner("🔍 البحث عن الأبحاث ذات الصلة..."):
            context_results = st.session_state.embedding_manager.semantic_search(last_query)

        if context_results:
            with st.expander("📚 سياق البحث ذو الصلة (انقر للعرض)"):
                for i, res in enumerate(context_results[:3]):  # Show top 3
                    st.markdown(f"**المصدر**: `{res['metadata']['filename']}`")
                    st.caption(res['text'][:250] + "...")
                    if st.button(f"استخدم المرجع {i+1}", key=f"ref_{i}"):
                        st.session_state.messages.append({
                            "role": "user",
                            "content": f"حلل هذا المقطع: {res['text'][:500]}"
                        })
                        st.rerun()

# Main chat interface with history
st.divider()
st.subheader("💬 مساعد الكتابة بالذكاء الاصطناعي")

for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# User input with history context
if prompt := st.chat_input("بماذا تريد المساعدة؟"):
    if not st.session_state.model_initialized:
        st.error("يرجى تهيئة النموذج في الشريط الجانبي أولاً!")
        st.stop()
     # Save user message
    st.session_state.messages.append({"role": "user", "content": prompt})
    st.session_state.chat_db.add_message(
        session_id=st.session_state.current_session,
        role="user",
        content=prompt,
        metadata={
            "task": task,
            "model": model_name,
            "temperature": temperature
        }
    )
    # Add user message to chat
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)

    # Prepare context with chat history
    context = {
        "task": task,
        "user_query": prompt,
        "pdf_context": "",
        "template_context": "",
        "citation_context": "",
        "chat_history": st.session_state.messages[-6:-1]  # Last 5 messages as context
    }

    if st.session_state.uploaded_files:
        context["pdf_context"] = "\n\n".join(
            [f"PDF: {f['name']}\nContent: {f['content'][:2000]}..."
             for f in st.session_state.uploaded_files]
        )

    if st.session_state.selected_template:
        context["template_context"] = str(st.session_state.selected_template)

    if st.session_state.citations:
        context["citation_context"] = "\n".join(
            [f"{cite['author']} ({cite['year']}): {cite['title']}"
             for cite in st.session_state.citations[:10]]  # Limit to 10 citations
        )

    # Get the appropriate prompt template
    prompt_template = get_prompt_template(task, context)

    # Generate response
    with st.chat_message("assistant"):
        message_placeholder = st.empty()
        full_response = ""

        try:
            for chunk in st.session_state.llm_provider.generate_stream(prompt_template):
                full_response += chunk
                message_placeholder.markdown(full_response + "▌")

            message_placeholder.markdown(full_response)

            # Add assistant response and save history
            st.session_state.messages.append({"role": "assistant", "content": full_response})
            save_chat_history()

            # Extract and store new citations
            new_citations = utils.extract_citations(full_response)
            for cite in new_citations:
                if not any(c["title"] == cite["title"] for c in st.session_state.citations):
                    st.session_state.citations.append(cite)

            st.session_state.messages.append({"role": "assistant", "content": full_response})

        except Exception as e:
            st.error(f"Generation failed: {str(e)}")
 # Save assistant response
    st.session_state.messages.append({"role": "assistant", "content": full_response})
    st.session_state.chat_db.add_message(
        session_id=st.session_state.current_session,
        role="assistant",
        content=full_response,
        metadata={
            "model": model_name,
            "temperature": temperature,
            "citations": new_citations
        }
    )
# Template editor (admin only)
if os.getenv("ADMIN_MODE") == "true":
    with st.expander("⚙️ Template Editor (Admin)", expanded=False):
        template_files = utils.get_template_files()
        selected_edit = st.selectbox(
            "Select template to edit",
            [f.replace(".json", "") for f in template_files],
            index=0
        )

        if selected_edit:
            template_content = utils.load_template(selected_edit)
            edited = st.text_area(
                "Edit template (JSON format)",
                value=json.dumps(template_content, indent=2),
                height=300
            )

            col1, col2 = st.columns(2)
            with col1:
                if st.button("Save Changes"):
                    try:
                        utils.save_template(selected_edit, json.loads(edited))
                        st.success("Template updated successfully!")
                    except json.JSONDecodeError:
                        st.error("Invalid JSON format")
# (Moved generate_pdf above its first usage)

