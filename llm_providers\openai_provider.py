from openai import OpenAI
from .base_provider import BaseLLMProvider
from typing import Generator
import os

class OpenAIProvider(BaseLLMProvider):
    def __init__(self, model: str = "gpt-4-turbo", temperature: float = 0.7, **kwargs):
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = model
        self.temperature = temperature
        self.kwargs = kwargs
    
    def generate_stream(self, prompt: str) -> Generator[str, None, None]:
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=self.temperature,
            stream=True,
            **self.kwargs
        )
        
        for chunk in response:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
    
    def get_model_list(self) -> list[str]:
        return [
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]
    
    @staticmethod
    def get_provider_name() -> str:
        return "OpenAI"