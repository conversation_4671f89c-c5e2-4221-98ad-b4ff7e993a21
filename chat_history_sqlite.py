import sqlite3
from datetime import datetime
from typing import List, Dict, Optional
import json
from pathlib import Path

class SQLiteChatHistory:
    def __init__(self, db_path: str = "chat_history.db"):
        self.db_path = Path(db_path)
        self._init_db()

    def _init_db(self):
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP,
                metadata TEXT
            )""")

            conn.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                message_id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                role TEXT,
                content TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT,
                FOREIGN KEY(session_id) REFERENCES sessions(session_id)
            )""")

            conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_session ON messages(session_id)
            """)

    def create_session(self, session_id: Optional[str] = None, metadata: Optional[dict] = None) -> str:
        """Create a new chat session"""
        session_id = session_id or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "INSERT OR IGNORE INTO sessions (session_id, last_accessed, metadata) VALUES (?, ?, ?)",
                (session_id, datetime.now().isoformat(), json.dumps(metadata or {}))
            )
            conn.commit()
        return session_id

    def add_message(self, session_id: str, role: str, content: str, metadata: Optional[dict] = None):
        """Add a message to a session"""
        with sqlite3.connect(self.db_path) as conn:
            # Update session last accessed time
            conn.execute(
                "UPDATE sessions SET last_accessed = ? WHERE session_id = ?",
                (datetime.now().isoformat(), session_id))

            # Insert message
            conn.execute(
                """INSERT INTO messages
                (session_id, role, content, metadata)
                VALUES (?, ?, ?, ?)""",
                (session_id, role, content, json.dumps(metadata or {})))

            conn.commit()

    def get_messages(self, session_id: str, limit: Optional[int] = None) -> List[Dict]:
        """Retrieve messages for a session"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            query = "SELECT role, content, timestamp, metadata FROM messages WHERE session_id = ? ORDER BY timestamp"
            if limit:
                query += f" LIMIT {limit}"
            cursor.execute(query, (session_id,))
            rows = cursor.fetchall()

        return [{
            "role": row[0],
            "content": row[1],
            "timestamp": row[2],
            "metadata": json.loads(row[3]) if row[3] else {}
        } for row in rows]

    def list_sessions(self, limit: int = 20) -> List[Dict]:
        """List all available sessions"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT s.session_id, s.created_at, s.last_accessed, s.metadata,
                       COUNT(m.message_id) as message_count
                FROM sessions s
                LEFT JOIN messages m ON s.session_id = m.session_id
                GROUP BY s.session_id
                ORDER BY s.last_accessed DESC
                LIMIT ?
            """, (limit,))
            rows = cursor.fetchall()

        return [{
            "session_id": row[0],
            "created_at": row[1],
            "last_accessed": row[2],
            "metadata": json.loads(row[3]) if row[3] else {},
            "message_count": row[4]
        } for row in rows]

    def delete_session(self, session_id: str) -> bool:
        """Delete a session and all its messages"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Delete all messages for this session
                conn.execute("DELETE FROM messages WHERE session_id = ?", (session_id,))

                # Delete the session itself
                conn.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))

                conn.commit()
                return True
        except Exception as e:
            print(f"Error deleting session {session_id}: {e}")
            return False

    def export_session(self, session_id: str, format: str = "json"):
        """Export session to various formats"""
        messages = self.get_messages(session_id)

        if format == "json":
            return json.dumps(messages, indent=2, ensure_ascii=False)
        elif format == "text":
            return "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
        elif format == "csv":
            import csv
            from io import StringIO
            output = StringIO()
            writer = csv.writer(output)
            writer.writerow(["Role", "Content", "Timestamp"])
            for msg in messages:
                writer.writerow([msg['role'], msg['content'], msg['timestamp']])
            return output.getvalue()
        elif format == "html":
            html = "<html><body>"
            for msg in messages:
                html += f"<h3>{msg['role']}</h3><p>{msg['content']}</p><hr>"
            html += "</body></html>"
            return html
        return None