from anthropic import Anthropic
from .base_provider import Base<PERSON><PERSON>rovider
from typing import Generator
import os

class AnthropicProvider(BaseLLMProvider):
    def __init__(self, model: str = "claude-3-opus-20240229", temperature: float = 0.7, **kwargs):
        self.client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        self.model = model
        self.temperature = temperature
        self.kwargs = kwargs
    
    def generate_stream(self, prompt: str) -> Generator[str, None, None]:
        with self.client.messages.stream(
            max_tokens=4096,
            messages=[{"role": "user", "content": prompt}],
            model=self.model,
            temperature=self.temperature,
            **self.kwargs
        ) as stream:
            for text in stream.text_stream:
                yield text
    
    def get_model_list(self) -> list[str]:
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-2.1",
            "claude-2.0"
        ]
    
    @staticmethod
    def get_provider_name() -> str:
        return "Anthropic Claude"