from openai import OpenAI
from .base_provider import BaseLLMProvider
from typing import Generator
import os

class OpenRouterProvider(BaseLLMProvider):
    def __init__(self, model: str = "anthropic/claude-3-opus", temperature: float = 0.7, **kwargs):
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=os.getenv("OPENROUTER_API_KEY")
        )
        self.model = model
        self.temperature = temperature
        self.kwargs = kwargs
    
    def generate_stream(self, prompt: str) -> Generator[str, None, None]:
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=self.temperature,
            stream=True,
            **self.kwargs
        )
        
        for chunk in response:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
    
    def get_model_list(self) -> list[str]:
        return [
            "anthropic/claude-3-opus",
            "anthropic/claude-3-sonnet",
            "openai/gpt-4-turbo",
            "google/gemini-pro",
            "meta-llama/llama-3-70b-instruct"
        ]
    
    @staticmethod
    def get_provider_name() -> str:
        return "OpenRouter"