from abc import ABC, abstractmethod
from typing import Generator, List

class BaseLLMProvider(ABC):
    @abstractmethod
    def generate_stream(self, prompt: str) -> Generator[str, None, None]:
        """Stream generated text from the LLM"""
        pass
    
    @abstractmethod
    def get_model_list(self) -> List[str]:
        """Get available models for this provider"""
        pass
    
    @staticmethod
    @abstractmethod
    def get_provider_name() -> str:
        """Get the display name of this provider"""
        pass