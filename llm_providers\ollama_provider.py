import requests
from typing import Generator, List
from .base_provider import BaseLLMProvider
import subprocess
import json
from langchain_ollama import OllamaLLM

class OllamaProvider(BaseLLMProvider):
    def __init__(self, model: str = None, temperature: float = 0.7, **kwargs):
        self.ollama_host = kwargs.get('ollama_host', 'http://localhost:11434')
        self.model = model or self.get_default_model()
        self.temperature = temperature
        self.kwargs = kwargs
        
        # Initialize the Ollama LLM
        self.llm = self._initialize_llm()
    
    def _initialize_llm(self):
        return OllamaLLM(
            model=self.model,
            temperature=self.temperature,
            base_url=self.ollama_host,
            **self.kwargs
        )
    
    def get_default_model(self) -> str:
        """Get the first available model as default"""
        models = self.get_available_models()
        return models[0]['name'] if models else 'llama2'
    
    def get_available_models(self) -> List[dict]:
        """Get list of available models from local Ollama installation"""
        try:
            # Try API first
            response = requests.get(f"{self.ollama_host}/api/tags")
            if response.status_code == 200:
                return response.json().get('models', [])
            
            # Fallback to CLI if API fails
            result = subprocess.run(
                ['ollama', 'list'],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                models = []
                lines = result.stdout.split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        models.append({
                            'name': parts[0],
                            'size': parts[1],
                            'modified': ' '.join(parts[2:])
                        })
                return models
        except Exception:
            pass
        
        # Fallback to some default models if both methods fail
        return [
            {'name': 'llama2'},
            {'name': 'mistral'},
            {'name': 'gemma:2b'}
        ]
    
    def generate_stream(self, prompt: str) -> Generator[str, None, None]:
        for chunk in self.llm.stream(prompt):
            yield chunk
    
    def get_model_list(self) -> List[str]:
        """Return just the model names for the UI dropdown"""
        return [model['name'] for model in self.get_available_models()]
    
    @staticmethod
    def get_provider_name() -> str:
        return "Ollama (Local)"