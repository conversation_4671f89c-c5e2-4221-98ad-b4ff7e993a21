import google.generativeai as genai
from .base_provider import <PERSON><PERSON><PERSON>rovider
from typing import Generator, List
import os
from dotenv import load_dotenv

class GeminiProvider(BaseLLMProvider):
    def __init__(self, model: str = "gemini-pro", temperature: float = 0.7, **kwargs):
        # Load environment variables
        load_dotenv()
        
        # Configure API key
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set. Please set it to use the Gemini provider.")
        
        genai.configure(api_key=self.api_key)
        
        # Set generation config
        self.generation_config = genai.types.GenerationConfig(
            temperature=temperature,
            max_output_tokens=kwargs.get('max_tokens', 2048)
        )
        
        self.model_name = model
        self.model = genai.GenerativeModel(model_name=model)

    def generate_stream(self, prompt: str) -> Generator[str, None, None]:
        """Stream generated text from Gemini"""
        response = self.model.generate_content(
            contents=prompt,
            generation_config=self.generation_config,
            stream=True
        )
        
        for chunk in response:
            if chunk.text:
                yield chunk.text

    def get_model_list(self) -> List[str]:
        """Return available Gemini models by calling the API"""
        if not hasattr(self, 'api_key') or not self.api_key:
            return []
        
        try:
            # Get list of models from the API
            models = genai.list_models()
            
            # Filter for generative models and extract model names
            model_names = []
            for model in models:
                # Only include models that support generateContent
                if 'generateContent' in model.supported_generation_methods:
                    model_names.append(model.name.replace('models/', ''))
            
            return model_names
        
        except Exception as e:
            print(f"Error fetching models: {e}")
            # Fallback to hardcoded list if API call fails
            return [
                "gemini-1.5-flash",
                "gemini-1.5-flash-8b",
                "gemini-1.5-pro",
                "gemini-pro",
                "gemini-pro-vision"
            ]

    @staticmethod
    def get_provider_name() -> str:
        """Return provider name"""
        return "Google Gemini"