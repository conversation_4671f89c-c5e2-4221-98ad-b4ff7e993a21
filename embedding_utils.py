import ollama
import faiss
import numpy as np
import chromadb
from typing import List, Dict
from chromadb.utils.embedding_functions import EmbeddingFunction
import streamlit as st
from chromadb.api.types import EmbeddingFunction

# here
class NomicEmbeddingFunction(EmbeddingFunction):
    def __init__(self):
        super().__init__()
        self.model_name = "nomic-embed-text"

    def __call__(self, texts: List[str]) -> List[List[float]]:
        embeddings = []
        for text in texts:
            try:
                response = ollama.embeddings(
                    model=self.model_name,
                    prompt=text
                )
                embeddings.append(response['embedding'])
            except Exception as e:
                st.error(f"Embedding failed for: {text[:50]}... Error: {str(e)}")
                embeddings.append([0.0]*768)  # Zero vector fallback
        return embeddings

class EmbeddingManager:
    def __init__(self):
        try:
            # Initialize components
            self.embedding_function = NomicEmbeddingFunction()  # Note the consistent name
            self.dimensions = 768

            # FAISS index
            self.index = faiss.IndexFlatL2(self.dimensions)

            # ChromaDB client
            self.chroma_client = chromadb.PersistentClient(
                path="./chroma_data",
                settings=chromadb.Settings(allow_reset=True)
            )

            # Create collection
            self.collection = self.chroma_client.get_or_create_collection(
                name="research_papers",
                embedding_function=self.embedding_function  # Consistent name here
            )

        except Exception as e:
            st.error(f"Embedding system initialization failed: {str(e)}")
            raise

    def add_to_index(self, text: str, metadata: dict = None):
        """Add document to vector stores"""
        try:
            # Get embeddings using the embedding function
            embeddings = self.embedding_function([text])

            # Add to FAISS
            self.index.add(np.array(embeddings))

            # Add to ChromaDB
            self.collection.add(
                documents=[text],
                embeddings=embeddings,
                metadatas=[metadata or {}],
                ids=[f"doc_{len(self.collection.get()['ids'])}"]
            )

        except Exception as e:
            st.error(f"Failed to index document: {str(e)}")
            raise

    def semantic_search(self, query: str, k: int = 3) -> List[Dict]:
        """Find most similar documents with hybrid search"""
        try:
            # Get query embedding using the embedding function
            query_embeddings = self.embedding_function([query])
            query_embedding = query_embeddings[0]

            # FAISS search
            distances, _ = self.index.search(np.array([query_embedding]), k)

            # ChromaDB search for metadata
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=k
            )

            # Return results with proper error handling
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i in range(min(k, len(results['documents'][0]))):
                    search_results.append({
                        "text": results['documents'][0][i],
                        "score": float(1 - distances[0][i]) if i < len(distances[0]) else 0.0,
                        "metadata": results['metadatas'][0][i] if results['metadatas'] and results['metadatas'][0] else {},
                        "source": results['metadatas'][0][i].get('filename', 'unknown') if results['metadatas'] and results['metadatas'][0] else 'unknown'
                    })

            return search_results

        except Exception as e:
            st.error(f"Semantic search failed: {str(e)}")
            return []

    def get_collection_stats(self) -> Dict:
        """Get statistics about stored embeddings"""
        try:
            return {
                "total_documents": self.collection.count(),
                "dimensions": self.dimensions,
                "faiss_index_size": self.index.ntotal
            }
        except Exception as e:
            st.error(f"Failed to get collection stats: {str(e)}")
            return {
                "total_documents": 0,
                "dimensions": self.dimensions,
                "faiss_index_size": 0
            }